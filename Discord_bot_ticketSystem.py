import discord
from discord.ext import commands
from discord.ui import <PERSON><PERSON>, View
import io
import asyncio

intents = discord.Intents.default()
intents.message_content = True
intents.guilds = True
intents.messages = True
intents.members = True

bot = commands.Bot(command_prefix="!!", intents=intents)

# 🔧 CONFIGURATION
SUPPORT_ROLE_ID = 762069032958427156  # Replace with your support role ID
TICKET_CATEGORY_ID = 761888422498926592  # Replace with category ID where threads should be created

@bot.event
async def on_ready():
    print(f"Bot is ready. Logged in as {bot.user}")

# View for ticket management buttons
class TicketManagementView(View):
    def __init__(self):
        super().__init__(timeout=None)
        self.add_item(SaveTranscriptButton())
        self.add_item(CloseTicketButton())

class SaveTranscriptButton(Button):
    def __init__(self):
        super().__init__(label="� Save Transcript", style=discord.ButtonStyle.secondary)

    async def callback(self, interaction: discord.Interaction):
        user_roles = [role.id for role in interaction.user.roles]
        if SUPPORT_ROLE_ID not in user_roles:
            await interaction.response.send_message("❌ You don't have permission to save transcripts.", ephemeral=True)
            return

        thread = interaction.channel
        transcript = await generate_transcript(thread)
        file = discord.File(fp=io.StringIO(transcript), filename=f"{thread.name}-transcript.txt")

        await interaction.response.send_message("📄 Transcript saved!", file=file, ephemeral=True)

class CloseTicketButton(Button):
    def __init__(self):
        super().__init__(label="🔒 Close Ticket", style=discord.ButtonStyle.danger)

    async def callback(self, interaction: discord.Interaction):
        user_roles = [role.id for role in interaction.user.roles]
        if SUPPORT_ROLE_ID not in user_roles:
            await interaction.response.send_message("❌ You don't have permission to close tickets.", ephemeral=True)
            return

        thread = interaction.channel
        transcript = await generate_transcript(thread)
        file = discord.File(fp=io.StringIO(transcript), filename=f"{thread.name}-transcript.txt")

        await interaction.response.send_message("📄 Ticket will be closed in 5 seconds. Final transcript saved.", file=file)

        await asyncio.sleep(5)
        await thread.delete()

async def generate_transcript(thread):
    """Generate a transcript of the thread"""
    messages = [msg async for msg in thread.history(limit=None, oldest_first=True)]
    transcript_lines = []

    for msg in messages:
        timestamp = msg.created_at.strftime('%Y-%m-%d %H:%M:%S UTC')
        author = f"{msg.author.display_name} ({msg.author})"
        content = msg.content if msg.content else "[No text content]"

        # Handle attachments
        if msg.attachments:
            attachments = ", ".join([att.filename for att in msg.attachments])
            content += f" [Attachments: {attachments}]"

        transcript_lines.append(f"[{timestamp}] {author}: {content}")

    return "\n".join(transcript_lines)

@bot.event
async def on_message(message):
    # Ignore bot messages
    if message.author.bot:
        return

    # Check if message contains !!ticket
    if "!!ticket" in message.content:
        await message.delete()
        await create_ticket(message.author, message.guild, message.channel)

    # Process other commands
    await bot.process_commands(message)

async def create_ticket(user, guild, original_channel):
    """Create a ticket thread for the user"""
    category = bot.get_channel(TICKET_CATEGORY_ID)

    if not category or not isinstance(category, discord.CategoryChannel):
        await original_channel.send("⚠️ Ticket category not found or misconfigured.", delete_after=10)
        return

    # Find a suitable channel in the category to create the thread
    # We'll use the first text channel in the category
    parent_channel = None
    for channel in category.channels:
        if isinstance(channel, discord.TextChannel):
            parent_channel = channel
            break

    if not parent_channel:
        await original_channel.send("⚠️ No suitable channel found in the ticket category.", delete_after=10)
        return

    # Create a thread from the parent channel
    thread = await parent_channel.create_thread(
        name=f"ticket-{user.name}-{user.discriminator}",
        type=discord.ChannelType.private_thread,
        invitable=False
    )

    await thread.add_user(user)

    support_role = guild.get_role(SUPPORT_ROLE_ID)
    if support_role:
        for member in support_role.members:
            await thread.add_user(member)

    await thread.send(
        f"🎫 Ticket created by {user.mention}. {support_role.mention if support_role else ''}",
        view=TicketManagementView()
    )

# Optional: persistent view for restarting bot
@bot.event
async def setup_hook():
    bot.add_view(TicketManagementView())  # Register persistent button view on startup

