import discord
from discord.ext import commands
from discord.ui import <PERSON><PERSON>, View
import datetime
import io

intents = discord.Intents.default()
intents.message_content = True
intents.guilds = True
intents.messages = True
intents.members = True

bot = commands.Bot(command_prefix="!!", intents=intents)

# 🔧 CONFIGURATION
SUPPORT_ROLE_ID = 123456789012345678  # Replace with your support role ID
TICKET_PARENT_CHANNEL_ID = 234567890123456789  # Replace with channel ID where threads should be created

@bot.event
async def on_ready():
    print(f"Bot is ready. Logged in as {bot.user}")

# View for the close button
class CloseTicketView(View):
    def __init__(self):
        super().__init__(timeout=None)
        self.add_item(CloseTicketButton())

class CloseTicketButton(Button):
    def __init__(self):
        super().__init__(label="🔒 Close Ticket", style=discord.ButtonStyle.danger)

    async def callback(self, interaction: discord.Interaction):
        user_roles = [role.id for role in interaction.user.roles]
        if SUPPORT_ROLE_ID not in user_roles:
            await interaction.response.send_message("❌ You don't have permission to close tickets.", ephemeral=True)
            return

        thread = interaction.channel

        # Save the transcript
        messages = [msg async for msg in thread.history(limit=None, oldest_first=True)]
        transcript = "\n".join(f"[{m.created_at.strftime('%Y-%m-%d %H:%M:%S')}] {m.author}: {m.content}" for m in messages)

        file = discord.File(fp=io.StringIO(transcript), filename=f"{thread.name}-transcript.txt")

        await interaction.response.send_message("📄 Ticket will be closed in 5 seconds. Transcript saved.", file=file)

        await discord.utils.sleep_until(datetime.datetime.utcnow() + datetime.timedelta(seconds=5))
        await thread.delete()

@bot.command()
async def ticket(ctx):
    await ctx.message.delete()
    parent_channel = bot.get_channel(TICKET_PARENT_CHANNEL_ID)

    if not parent_channel:
        return await ctx.send("⚠️ Support channel not found or misconfigured.")

    # Create a thread from the parent channel
    thread = await parent_channel.create_thread(
        name=f"ticket-{ctx.author.name}-{ctx.author.discriminator}",
        type=discord.ChannelType.private_thread,
        invitable=False
    )

    await thread.add_user(ctx.author)

    support_role = ctx.guild.get_role(SUPPORT_ROLE_ID)
    if support_role:
        for member in support_role.members:
            await thread.add_user(member)

    await thread.send(
        f"🎫 Ticket created by {ctx.author.mention}. {support_role.mention if support_role else ''}",
        view=CloseTicketView()
    )

# Optional: persistent view for restarting bot
@bot.event
async def setup_hook():
    bot.add_view(CloseTicketView())  # Register persistent button view on startup

